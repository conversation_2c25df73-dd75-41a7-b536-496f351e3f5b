import { FormsService, CreateFormDto, UpdateFormDto } from './forms.service';
import { Form } from '../entities/form.entity';
export declare class FormsController {
    private readonly formsService;
    constructor(formsService: FormsService);
    findAll(): Promise<Form[]>;
    findOne(id: string): Promise<Form>;
    create(createFormDto: CreateFormDto): Promise<Form>;
    update(id: string, updateFormDto: UpdateFormDto): Promise<Form>;
    remove(id: string): Promise<{
        message: string;
    }>;
    duplicate(id: string): Promise<Form>;
}
