<template>
  <div class="forms-container">
    <div class="forms-header">
      <h1>表单管理</h1>
      <div class="header-actions">
        <router-link to="/editor" class="create-button">
          <span class="icon">➕</span>
          创建新表单
        </router-link>
      </div>
    </div>

    <div class="forms-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <p>加载中...</p>
      </div>

      <!-- 表单列表 -->
      <div v-else-if="forms.length > 0" class="forms-grid">
        <div v-for="form in forms" :key="form.id" class="form-card">
          <div class="form-card-header">
            <h3>{{ form.name }}</h3>
            <span class="form-status" :class="form.status">
              {{ getStatusText(form.status) }}
            </span>
          </div>

          <div class="form-card-body">
            <p class="form-description">
              {{ form.description || "暂无描述" }}
            </p>
            <div class="form-meta">
              <span>创建时间: {{ formatDate(form.createdAt) }}</span>
              <span>更新时间: {{ formatDate(form.updatedAt) }}</span>
            </div>
          </div>

          <div class="form-card-actions">
            <button @click="editForm(form.id)" class="action-btn edit-btn">
              编辑
            </button>
            <button
              @click="duplicateForm(form.id)"
              class="action-btn duplicate-btn"
            >
              复制
            </button>
            <button @click="deleteForm(form.id)" class="action-btn delete-btn">
              删除
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>还没有表单</h3>
        <p>创建您的第一个表单开始使用低代码编辑器</p>
        <router-link to="/editor" class="create-button">
          创建新表单
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { formsApi } from "../api/forms.js";

const router = useRouter();

// 响应式数据
const forms = ref([]);
const loading = ref(true);

// 获取表单列表
const loadForms = async () => {
  loading.value = true;
  try {
    forms.value = await formsApi.getAll();
  } catch (error) {
    console.error("加载表单列表失败:", error);
    alert("加载表单列表失败: " + error.message);
  } finally {
    loading.value = false;
  }
};

// 编辑表单
const editForm = (formId) => {
  router.push(`/editor?id=${formId}`);
};

// 复制表单
const duplicateForm = async (formId) => {
  try {
    await formsApi.duplicate(formId);
    alert("表单复制成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("复制表单失败:", error);
    alert("复制表单失败: " + error.message);
  }
};

// 删除表单
const deleteForm = async (formId) => {
  if (!confirm("确定要删除这个表单吗？此操作不可恢复。")) {
    return;
  }

  try {
    await formsApi.delete(formId);
    alert("表单删除成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("删除表单失败:", error);
    alert("删除表单失败: " + error.message);
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    draft: "草稿",
    published: "已发布",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadForms();
});
</script>

<style scoped>
.forms-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  background-color: #fafbfc;
}

.forms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.forms-header h1 {
  color: #41b883;
  margin: 0;
}

.create-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #41b883;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.create-button:hover {
  background-color: #35495e;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

.form-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: box-shadow 0.2s, transform 0.2s;
}

.form-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.form-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.form-card-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  line-height: 1.4;
}

.form-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.form-status.draft {
  background-color: #f0f9ff;
  color: #0369a1;
}

.form-status.published {
  background-color: #f0fdf4;
  color: #166534;
}

.form-status.archived {
  background-color: #fafafa;
  color: #525252;
}

.form-card-body {
  margin-bottom: 20px;
}

.form-description {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.form-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.form-card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.edit-btn {
  background-color: #41b883;
  color: white;
}

.edit-btn:hover {
  background-color: #35495e;
}

.duplicate-btn {
  background-color: #f39c12;
  color: white;
}

.duplicate-btn:hover {
  background-color: #e67e22;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
  margin-bottom: 30px;
}
</style>
