<template>
  <div class="el-input">
    <input 
      type="text" 
      class="el-input__inner" 
      :placeholder="placeholder"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
    >
    <span 
      v-if="clearable && modelValue" 
      class="el-input__clear"
      @click="$emit('update:modelValue', '')"
    >
      ×
    </span>
  </div>
</template>

<script setup>
defineProps({
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  modelValue: {
    type: String,
    default: ''
  },
  clearable: {
    type: Boolean,
    default: false
  }
});

defineEmits(['update:modelValue']);
</script>

<style scoped>
.el-input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}

.el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  width: 100%;
}

.el-input__inner:focus {
  outline: none;
  border-color: #409eff;
}

.el-input__inner::placeholder {
  color: #c0c4cc;
}

.el-input__clear {
  position: absolute;
  top: 0;
  right: 10px;
  height: 40px;
  line-height: 40px;
  color: #c0c4cc;
  cursor: pointer;
  font-size: 16px;
}

.el-input__clear:hover {
  color: #909399;
}
</style>
