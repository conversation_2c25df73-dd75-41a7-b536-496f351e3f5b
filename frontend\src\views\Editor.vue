<template>
  <div class="editor-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <h2>表单编辑器</h2>
      <div class="toolbar-actions">
        <button
          @click="showSaveDialog = true"
          :disabled="designElements.length === 0"
        >
          保存表单
        </button>
        <button @click="previewDesign">预览</button>
        <button @click="clearCanvas">清空画布</button>
        <router-link to="/forms" class="manage-button">管理表单</router-link>
      </div>
    </div>

    <div class="editor-main">
      <!-- 物料区 -->
      <div class="materials-panel">
        <h3>物料区</h3>
        <div class="materials-list">
          <div
            v-for="component in componentList"
            :key="component.type"
            class="material-item"
            draggable="true"
            @dragstart="onDragStart($event, component)"
          >
            <div class="material-icon">
              <i :class="component.icon"></i>
            </div>
            <div class="material-name">{{ component.label }}</div>
          </div>
        </div>
      </div>

      <!-- 渲染器 -->
      <div class="renderer" @dragover.prevent @drop="onDrop">
        <h3>渲染器</h3>
        <div class="canvas" ref="canvas">
          <template v-if="designElements.length > 0">
            <div
              v-for="(element, index) in designElements"
              :key="index"
              class="design-element"
              :class="{ selected: selectedElementIndex === index }"
              @click.stop="selectElement(index)"
            >
              <component :is="element.type" v-bind="element.props" />
            </div>
          </template>
          <div v-else class="empty-tip">拖拽左侧组件到此处</div>
        </div>
      </div>

      <!-- 设置器 -->
      <div class="properties-panel">
        <h3>设置器</h3>
        <div v-if="selectedElement" class="properties-form">
          <h4>{{ selectedElement.componentName }}</h4>

          <div
            class="property-item"
            v-for="(value, key) in selectedElement.props"
            :key="key"
          >
            <label>{{ getPropLabel(key) }}</label>
            <input
              v-if="typeof value === 'string'"
              type="text"
              v-model="selectedElement.props[key]"
            />
            <input
              v-else-if="typeof value === 'number'"
              type="number"
              v-model.number="selectedElement.props[key]"
            />
            <input
              v-else-if="typeof value === 'boolean'"
              type="checkbox"
              v-model="selectedElement.props[key]"
            />
          </div>
        </div>
        <div v-else class="empty-tip">请选择一个组件进行设置</div>
      </div>
    </div>

    <!-- 保存对话框 -->
    <div
      v-if="showSaveDialog"
      class="modal-overlay"
      @click="showSaveDialog = false"
    >
      <div class="modal-content" @click.stop>
        <h3>保存表单</h3>
        <form @submit.prevent="saveForm">
          <div class="form-group">
            <label for="formName">表单名称 *</label>
            <input
              id="formName"
              v-model="saveFormData.name"
              type="text"
              required
              placeholder="请输入表单名称"
            />
          </div>
          <div class="form-group">
            <label for="formDescription">表单描述</label>
            <textarea
              id="formDescription"
              v-model="saveFormData.description"
              placeholder="请输入表单描述（可选）"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="formStatus">状态</label>
            <select id="formStatus" v-model="saveFormData.status">
              <option value="draft">草稿</option>
              <option value="published">已发布</option>
            </select>
          </div>
          <div class="modal-actions">
            <button type="button" @click="showSaveDialog = false">取消</button>
            <button type="submit" :disabled="saving">
              {{ saving ? "保存中..." : "保存" }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { formsApi } from "../api/forms.js";
import ElButton from "../components/editor/ElButton.vue";
import ElInput from "../components/editor/ElInput.vue";
import ElText from "../components/editor/ElText.vue";

const route = useRoute();
const router = useRouter();

// 保存相关状态
const showSaveDialog = ref(false);
const saving = ref(false);
const currentFormId = ref(null);
const saveFormData = ref({
  name: "",
  description: "",
  status: "draft",
});

// 组件列表
const componentList = ref([
  {
    type: "ElButton",
    label: "按钮",
    icon: "el-icon-thumb",
    defaultProps: {
      type: "primary",
      size: "default",
      text: "按钮",
    },
  },
  {
    type: "ElInput",
    label: "输入框",
    icon: "el-icon-edit",
    defaultProps: {
      placeholder: "请输入内容",
      clearable: true,
    },
  },
  {
    type: "ElText",
    label: "文本",
    icon: "el-icon-document",
    defaultProps: {
      content: "文本内容",
    },
  },
]);

// 设计元素
const designElements = ref([]);
const selectedElementIndex = ref(-1);

// 计算属性：当前选中的元素
const selectedElement = computed(() => {
  if (
    selectedElementIndex.value >= 0 &&
    selectedElementIndex.value < designElements.value.length
  ) {
    return designElements.value[selectedElementIndex.value];
  }
  return null;
});

// 拖拽开始
const onDragStart = (event, component) => {
  event.dataTransfer.setData("componentType", component.type);
};

// 放置元素
const onDrop = (event) => {
  const componentType = event.dataTransfer.getData("componentType");
  const component = componentList.value.find(
    (item) => item.type === componentType
  );

  if (component) {
    // 创建新元素
    const newElement = {
      type: component.type,
      componentName: component.label,
      props: { ...component.defaultProps },
    };

    // 添加到设计元素列表
    designElements.value.push(newElement);

    // 自动选中新添加的元素
    selectedElementIndex.value = designElements.value.length - 1;
  }
};

// 选择元素
const selectElement = (index) => {
  selectedElementIndex.value = index;
};

// 获取属性标签
const getPropLabel = (key) => {
  const labelMap = {
    text: "文本",
    type: "类型",
    size: "大小",
    placeholder: "占位文本",
    clearable: "可清除",
    content: "内容",
  };

  return labelMap[key] || key;
};

// 保存表单
const saveForm = async () => {
  if (!saveFormData.value.name.trim()) {
    alert("请输入表单名称");
    return;
  }

  saving.value = true;
  try {
    const formData = {
      name: saveFormData.value.name.trim(),
      description: saveFormData.value.description.trim(),
      content: JSON.stringify(designElements.value),
      status: saveFormData.value.status,
    };

    let result;
    if (currentFormId.value) {
      // 更新现有表单
      result = await formsApi.update(currentFormId.value, formData);
    } else {
      // 创建新表单
      result = await formsApi.create(formData);
      currentFormId.value = result.id;
    }

    showSaveDialog.value = false;
    alert("表单保存成功！");

    // 重置保存表单数据
    saveFormData.value = {
      name: "",
      description: "",
      status: "draft",
    };
  } catch (error) {
    console.error("保存失败:", error);
    alert("保存失败: " + error.message);
  } finally {
    saving.value = false;
  }
};

// 预览设计
const previewDesign = () => {
  console.log("预览设计");
  // 这里可以实现预览逻辑
};

// 清空画布
const clearCanvas = () => {
  if (confirm("确定要清空画布吗？")) {
    designElements.value = [];
    selectedElementIndex.value = -1;
    currentFormId.value = null;
  }
};

// 加载表单
const loadForm = async (formId) => {
  try {
    const form = await formsApi.getById(formId);
    const content = JSON.parse(form.content);
    designElements.value = content;
    currentFormId.value = form.id;

    // 设置保存表单数据
    saveFormData.value = {
      name: form.name,
      description: form.description || "",
      status: form.status,
    };
  } catch (error) {
    console.error("加载表单失败:", error);
    alert("加载表单失败: " + error.message);
  }
};

// 组件挂载时检查是否有表单ID参数
onMounted(() => {
  const formId = route.query.id;
  if (formId) {
    loadForm(formId);
  }
});
</script>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.toolbar {
  height: 60px;
  border-bottom: 1px solid #e1e8ed;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.toolbar-actions button {
  padding: 6px 12px;
  background-color: #41b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.toolbar-actions button:hover {
  background-color: #35495e;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 60px);
}

.materials-panel {
  width: 240px;
  border-right: 1px solid #e1e8ed;
  padding: 15px;
  overflow-y: auto;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.material-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: move;
  transition: background-color 0.2s;
}

.material-item:hover {
  background-color: #f5f5f5;
}

.material-icon {
  margin-right: 10px;
}

.renderer {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
}

.canvas {
  min-height: calc(100vh - 200px);
  height: 100%;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  margin-top: 15px;
  background-color: #fafbfc;
}

.properties-panel {
  width: 280px;
  border-left: 1px solid #e1e8ed;
  padding: 15px;
  overflow-y: auto;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.properties-form {
  margin-top: 15px;
}

.property-item {
  margin-bottom: 15px;
}

.property-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.property-item input[type="text"],
.property-item input[type="number"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-style: italic;
}

.design-element {
  margin-bottom: 10px;
  padding: 5px;
  border: 2px solid transparent;
  border-radius: 4px;
}

.design-element.selected {
  border-color: #41b883;
}

/* 管理按钮样式 */
.manage-button {
  padding: 6px 12px;
  background-color: #67c23a;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.manage-button:hover {
  background-color: #5daf34;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #41b883;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.modal-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.modal-actions button[type="button"] {
  background-color: #f5f5f5;
  color: #333;
}

.modal-actions button[type="submit"] {
  background-color: #41b883;
  color: white;
}

.modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-actions button:hover:not(:disabled) {
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .materials-panel {
    width: 200px;
  }

  .properties-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .editor-main {
    flex-direction: column;
  }

  .materials-panel,
  .properties-panel {
    width: 100%;
    height: 200px;
    border: none;
    border-bottom: 1px solid #e1e8ed;
  }

  .renderer {
    flex: 1;
    min-height: 400px;
  }

  .canvas {
    min-height: 300px;
  }
}
</style>
