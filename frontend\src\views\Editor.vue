<template>
  <div class="editor-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <h2>表单编辑器</h2>
      <div class="toolbar-actions">
        <button
          @click="showSaveDialog = true"
          :disabled="designElements.length === 0"
        >
          保存表单
        </button>
        <button @click="previewDesign">预览</button>
        <button @click="clearCanvas">清空画布</button>
        <router-link to="/forms" class="manage-button">管理表单</router-link>
      </div>
    </div>

    <div class="editor-main">
      <!-- 物料区 -->
      <div class="materials-panel">
        <h3>物料区</h3>
        <div class="materials-list">
          <div
            v-for="component in componentList"
            :key="component.type"
            class="material-item"
            draggable="true"
            @dragstart="onDragStart($event, component)"
          >
            <div class="material-icon">
              <i :class="component.icon"></i>
            </div>
            <div class="material-name">{{ component.label }}</div>
          </div>
        </div>
      </div>

      <!-- 渲染器 -->
      <div class="renderer" @dragover.prevent @drop="onDrop">
        <div class="renderer-header">
          <h3>画布</h3>
          <div class="canvas-controls">
            <button @click="toggleGrid" :class="{ active: showGrid }">
              {{ showGrid ? "隐藏网格" : "显示网格" }}
            </button>
            <select v-model="gridSize" @change="updateGridSize">
              <option value="20">网格 20px</option>
              <option value="30">网格 30px</option>
              <option value="40">网格 40px</option>
              <option value="50">网格 50px</option>
            </select>
          </div>
        </div>
        <div
          class="canvas"
          ref="canvas"
          :class="{ 'show-grid': showGrid }"
          :style="canvasStyle"
          @mousedown="onCanvasMouseDown"
          @mousemove="onCanvasMouseMove"
          @mouseup="onCanvasMouseUp"
        >
          <template v-if="designElements.length > 0">
            <div
              v-for="(element, index) in designElements"
              :key="element.id || index"
              class="design-element"
              :class="{
                selected: selectedElementIndex === index,
                dragging: draggingIndex === index,
              }"
              :style="getElementStyle(element)"
              @click.stop="selectElement(index)"
              @mousedown.stop="startElementDrag(index, $event)"
            >
              <component :is="element.type" v-bind="element.props" />

              <!-- 选中状态的控制点 -->
              <div
                v-if="selectedElementIndex === index"
                class="element-controls"
              >
                <div class="resize-handles">
                  <div
                    v-for="handle in resizeHandles"
                    :key="handle"
                    :class="`resize-handle resize-${handle}`"
                    @mousedown.stop="startResize(index, handle, $event)"
                  ></div>
                </div>
              </div>
            </div>
          </template>
          <div v-else class="empty-tip">拖拽左侧组件到此处开始设计</div>
        </div>
      </div>

      <!-- 设置器 -->
      <div class="properties-panel">
        <h3>设置器</h3>
        <div v-if="selectedElement" class="properties-form">
          <h4>{{ selectedElement.componentName }}</h4>

          <!-- 位置和大小控制 -->
          <div class="property-section">
            <h5>位置和大小</h5>
            <div class="property-row">
              <div class="property-item">
                <label>X 位置</label>
                <input
                  type="number"
                  :value="selectedElement.position?.x || 0"
                  @input="updateElementPosition('x', $event.target.value)"
                />
              </div>
              <div class="property-item">
                <label>Y 位置</label>
                <input
                  type="number"
                  :value="selectedElement.position?.y || 0"
                  @input="updateElementPosition('y', $event.target.value)"
                />
              </div>
            </div>
            <div class="property-row">
              <div class="property-item">
                <label>宽度</label>
                <input
                  type="number"
                  :value="selectedElement.size?.width || 120"
                  @input="updateElementSize('width', $event.target.value)"
                />
              </div>
              <div class="property-item">
                <label>高度</label>
                <input
                  type="number"
                  :value="selectedElement.size?.height || 40"
                  @input="updateElementSize('height', $event.target.value)"
                />
              </div>
            </div>
          </div>

          <!-- 组件属性 -->
          <div class="property-section">
            <h5>组件属性</h5>
            <div
              class="property-item"
              v-for="(value, key) in selectedElement.props"
              :key="key"
            >
              <label>{{ getPropLabel(key) }}</label>
              <input
                v-if="typeof value === 'string'"
                type="text"
                v-model="selectedElement.props[key]"
              />
              <input
                v-else-if="typeof value === 'number'"
                type="number"
                v-model.number="selectedElement.props[key]"
              />
              <input
                v-else-if="typeof value === 'boolean'"
                type="checkbox"
                v-model="selectedElement.props[key]"
              />
            </div>
          </div>
        </div>
        <div v-else class="empty-tip">请选择一个组件进行设置</div>
      </div>
    </div>

    <!-- 保存对话框 -->
    <div
      v-if="showSaveDialog"
      class="modal-overlay"
      @click="showSaveDialog = false"
    >
      <div class="modal-content" @click.stop>
        <h3>保存表单</h3>
        <form @submit.prevent="saveForm">
          <div class="form-group">
            <label for="formName">表单名称 *</label>
            <input
              id="formName"
              v-model="saveFormData.name"
              type="text"
              required
              placeholder="请输入表单名称"
            />
          </div>
          <div class="form-group">
            <label for="formDescription">表单描述</label>
            <textarea
              id="formDescription"
              v-model="saveFormData.description"
              placeholder="请输入表单描述（可选）"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="formStatus">状态</label>
            <select id="formStatus" v-model="saveFormData.status">
              <option value="draft">草稿</option>
              <option value="published">已发布</option>
            </select>
          </div>
          <div class="modal-actions">
            <button type="button" @click="showSaveDialog = false">取消</button>
            <button type="submit" :disabled="saving">
              {{ saving ? "保存中..." : "保存" }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { formsApi } from "../api/forms.js";
import ElButton from "../components/editor/ElButton.vue";
import ElInput from "../components/editor/ElInput.vue";
import ElText from "../components/editor/ElText.vue";

const route = useRoute();
const router = useRouter();

// 保存相关状态
const showSaveDialog = ref(false);
const saving = ref(false);
const currentFormId = ref(null);
const saveFormData = ref({
  name: "",
  description: "",
  status: "draft",
});

// 组件列表
const componentList = ref([
  {
    type: "ElButton",
    label: "按钮",
    icon: "el-icon-thumb",
    defaultProps: {
      type: "primary",
      size: "default",
      text: "按钮",
    },
  },
  {
    type: "ElInput",
    label: "输入框",
    icon: "el-icon-edit",
    defaultProps: {
      placeholder: "请输入内容",
      clearable: true,
    },
  },
  {
    type: "ElText",
    label: "文本",
    icon: "el-icon-document",
    defaultProps: {
      content: "文本内容",
    },
  },
]);

// 设计元素
const designElements = ref([]);
const selectedElementIndex = ref(-1);

// Grid 相关状态
const showGrid = ref(true);
const gridSize = ref(30);

// 拖拽相关状态
const draggingIndex = ref(-1);
const dragStartPos = ref({ x: 0, y: 0 });
const elementStartPos = ref({ x: 0, y: 0 });
const isDragging = ref(false);

// 调整大小相关状态
const isResizing = ref(false);
const resizeHandle = ref("");
const resizeStartPos = ref({ x: 0, y: 0 });
const resizeStartSize = ref({ width: 0, height: 0 });

// 调整大小控制点
const resizeHandles = ["nw", "ne", "sw", "se", "n", "s", "w", "e"];

// 计算属性：当前选中的元素
const selectedElement = computed(() => {
  if (
    selectedElementIndex.value >= 0 &&
    selectedElementIndex.value < designElements.value.length
  ) {
    return designElements.value[selectedElementIndex.value];
  }
  return null;
});

// 计算属性：画布样式
const canvasStyle = computed(() => {
  return {
    backgroundImage: showGrid.value
      ? `linear-gradient(to right, #e1e8ed 1px, transparent 1px),
         linear-gradient(to bottom, #e1e8ed 1px, transparent 1px)`
      : "none",
    backgroundSize: showGrid.value
      ? `${gridSize.value}px ${gridSize.value}px`
      : "auto",
  };
});

// 拖拽开始
const onDragStart = (event, component) => {
  event.dataTransfer.setData("componentType", component.type);
};

// 放置元素
const onDrop = (event) => {
  const componentType = event.dataTransfer.getData("componentType");
  const component = componentList.value.find(
    (item) => item.type === componentType
  );

  if (component) {
    const canvas = event.currentTarget;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 对齐到网格
    const gridX = Math.round(x / gridSize.value) * gridSize.value;
    const gridY = Math.round(y / gridSize.value) * gridSize.value;

    // 创建新元素
    const newElement = {
      id: Date.now() + Math.random(), // 唯一ID
      type: component.type,
      componentName: component.label,
      props: { ...component.defaultProps },
      position: {
        x: gridX,
        y: gridY,
      },
      size: {
        width: 120,
        height: 40,
      },
    };

    // 添加到设计元素列表
    designElements.value.push(newElement);

    // 自动选中新添加的元素
    selectedElementIndex.value = designElements.value.length - 1;
  }
};

// 选择元素
const selectElement = (index) => {
  selectedElementIndex.value = index;
};

// 获取元素样式
const getElementStyle = (element) => {
  if (!element.position || !element.size) {
    return {};
  }

  return {
    position: "absolute",
    left: `${element.position.x}px`,
    top: `${element.position.y}px`,
    width: `${element.size.width}px`,
    height: `${element.size.height}px`,
    zIndex: 1,
  };
};

// 网格控制
const toggleGrid = () => {
  showGrid.value = !showGrid.value;
};

const updateGridSize = () => {
  // 网格大小更新时重新对齐所有元素
  designElements.value.forEach((element) => {
    if (element.position) {
      element.position.x =
        Math.round(element.position.x / gridSize.value) * gridSize.value;
      element.position.y =
        Math.round(element.position.y / gridSize.value) * gridSize.value;
    }
  });
};

// 获取属性标签
const getPropLabel = (key) => {
  const labelMap = {
    text: "文本",
    type: "类型",
    size: "大小",
    placeholder: "占位文本",
    clearable: "可清除",
    content: "内容",
  };

  return labelMap[key] || key;
};

// 元素拖拽
const startElementDrag = (index, event) => {
  if (event.button !== 0) return; // 只响应左键

  draggingIndex.value = index;
  isDragging.value = true;

  const element = designElements.value[index];
  dragStartPos.value = {
    x: event.clientX,
    y: event.clientY,
  };

  elementStartPos.value = {
    x: element.position.x,
    y: element.position.y,
  };

  document.addEventListener("mousemove", onElementDragMove);
  document.addEventListener("mouseup", onElementDragEnd);

  event.preventDefault();
};

const onElementDragMove = (event) => {
  if (!isDragging.value || draggingIndex.value === -1) return;

  const deltaX = event.clientX - dragStartPos.value.x;
  const deltaY = event.clientY - dragStartPos.value.y;

  const newX = elementStartPos.value.x + deltaX;
  const newY = elementStartPos.value.y + deltaY;

  // 对齐到网格
  const gridX = Math.round(newX / gridSize.value) * gridSize.value;
  const gridY = Math.round(newY / gridSize.value) * gridSize.value;

  // 确保不超出画布边界
  const element = designElements.value[draggingIndex.value];
  const canvas = document.querySelector(".canvas");
  if (canvas) {
    const maxX = canvas.clientWidth - element.size.width;
    const maxY = canvas.clientHeight - element.size.height;

    element.position.x = Math.max(0, Math.min(gridX, maxX));
    element.position.y = Math.max(0, Math.min(gridY, maxY));
  }
};

const onElementDragEnd = () => {
  isDragging.value = false;
  draggingIndex.value = -1;

  document.removeEventListener("mousemove", onElementDragMove);
  document.removeEventListener("mouseup", onElementDragEnd);
};

// 画布事件处理
const onCanvasMouseDown = (event) => {
  if (event.target.classList.contains("canvas")) {
    selectedElementIndex.value = -1;
  }
};

const onCanvasMouseMove = () => {
  // 可以在这里添加鼠标移动时的逻辑
};

const onCanvasMouseUp = () => {
  // 可以在这里添加鼠标释放时的逻辑
};

// 调整大小
const startResize = (index, handle, event) => {
  if (event.button !== 0) return;

  isResizing.value = true;
  resizeHandle.value = handle;

  const element = designElements.value[index];
  resizeStartPos.value = {
    x: event.clientX,
    y: event.clientY,
  };

  resizeStartSize.value = {
    width: element.size.width,
    height: element.size.height,
  };

  document.addEventListener("mousemove", onResizeMove);
  document.addEventListener("mouseup", onResizeEnd);

  event.preventDefault();
};

const onResizeMove = (event) => {
  if (!isResizing.value || selectedElementIndex.value === -1) return;

  const deltaX = event.clientX - resizeStartPos.value.x;
  const deltaY = event.clientY - resizeStartPos.value.y;

  const element = designElements.value[selectedElementIndex.value];
  const handle = resizeHandle.value;

  let newWidth = resizeStartSize.value.width;
  let newHeight = resizeStartSize.value.height;
  let newX = element.position.x;
  let newY = element.position.y;

  // 根据拖拽的控制点调整大小和位置
  if (handle.includes("e")) {
    newWidth = Math.max(20, resizeStartSize.value.width + deltaX);
  }
  if (handle.includes("w")) {
    newWidth = Math.max(20, resizeStartSize.value.width - deltaX);
    newX = element.position.x + (resizeStartSize.value.width - newWidth);
  }
  if (handle.includes("s")) {
    newHeight = Math.max(20, resizeStartSize.value.height + deltaY);
  }
  if (handle.includes("n")) {
    newHeight = Math.max(20, resizeStartSize.value.height - deltaY);
    newY = element.position.y + (resizeStartSize.value.height - newHeight);
  }

  // 对齐到网格
  newWidth = Math.round(newWidth / gridSize.value) * gridSize.value;
  newHeight = Math.round(newHeight / gridSize.value) * gridSize.value;
  newX = Math.round(newX / gridSize.value) * gridSize.value;
  newY = Math.round(newY / gridSize.value) * gridSize.value;

  element.size.width = newWidth;
  element.size.height = newHeight;
  element.position.x = newX;
  element.position.y = newY;
};

const onResizeEnd = () => {
  isResizing.value = false;
  resizeHandle.value = "";

  document.removeEventListener("mousemove", onResizeMove);
  document.removeEventListener("mouseup", onResizeEnd);
};

// 更新元素位置
const updateElementPosition = (axis, value) => {
  if (selectedElementIndex.value === -1) return;

  const element = designElements.value[selectedElementIndex.value];
  if (!element.position) {
    element.position = { x: 0, y: 0 };
  }

  const numValue = parseInt(value) || 0;
  const gridValue = Math.round(numValue / gridSize.value) * gridSize.value;

  element.position[axis] = Math.max(0, gridValue);
};

// 更新元素大小
const updateElementSize = (dimension, value) => {
  if (selectedElementIndex.value === -1) return;

  const element = designElements.value[selectedElementIndex.value];
  if (!element.size) {
    element.size = { width: 120, height: 40 };
  }

  const numValue = parseInt(value) || 20;
  const gridValue = Math.round(numValue / gridSize.value) * gridSize.value;

  element.size[dimension] = Math.max(20, gridValue);
};

// 保存表单
const saveForm = async () => {
  if (!saveFormData.value.name.trim()) {
    alert("请输入表单名称");
    return;
  }

  saving.value = true;
  try {
    const formData = {
      name: saveFormData.value.name.trim(),
      description: saveFormData.value.description.trim(),
      content: JSON.stringify(designElements.value),
      status: saveFormData.value.status,
    };

    let result;
    if (currentFormId.value) {
      // 更新现有表单
      result = await formsApi.update(currentFormId.value, formData);
    } else {
      // 创建新表单
      result = await formsApi.create(formData);
      currentFormId.value = result.id;
    }

    showSaveDialog.value = false;
    alert("表单保存成功！");

    // 重置保存表单数据
    saveFormData.value = {
      name: "",
      description: "",
      status: "draft",
    };
  } catch (error) {
    console.error("保存失败:", error);
    alert("保存失败: " + error.message);
  } finally {
    saving.value = false;
  }
};

// 预览设计
const previewDesign = () => {
  console.log("预览设计");
  // 这里可以实现预览逻辑
};

// 清空画布
const clearCanvas = () => {
  if (confirm("确定要清空画布吗？")) {
    designElements.value = [];
    selectedElementIndex.value = -1;
    currentFormId.value = null;
  }
};

// 加载表单
const loadForm = async (formId) => {
  try {
    const form = await formsApi.getById(formId);
    const content = JSON.parse(form.content);
    designElements.value = content;
    currentFormId.value = form.id;

    // 设置保存表单数据
    saveFormData.value = {
      name: form.name,
      description: form.description || "",
      status: form.status,
    };
  } catch (error) {
    console.error("加载表单失败:", error);
    alert("加载表单失败: " + error.message);
  }
};

// 组件挂载时检查是否有表单ID参数
onMounted(() => {
  const formId = route.query.id;
  if (formId) {
    loadForm(formId);
  }
});
</script>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.toolbar {
  height: 60px;
  border-bottom: 1px solid #e1e8ed;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.toolbar-actions button {
  padding: 6px 12px;
  background-color: #41b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.toolbar-actions button:hover {
  background-color: #35495e;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 60px);
}

.materials-panel {
  width: 240px;
  border-right: 1px solid #e1e8ed;
  padding: 15px;
  overflow-y: auto;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.material-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: move;
  transition: background-color 0.2s;
}

.material-item:hover {
  background-color: #f5f5f5;
}

.material-icon {
  margin-right: 10px;
}

.renderer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
}

.renderer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e8ed;
  background-color: #fafbfc;
}

.canvas-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.canvas-controls button {
  padding: 6px 12px;
  border: 1px solid #e1e8ed;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.canvas-controls button.active {
  background-color: #41b883;
  color: white;
  border-color: #41b883;
}

.canvas-controls select {
  padding: 6px 8px;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  font-size: 12px;
}

.canvas {
  flex: 1;
  position: relative;
  margin: 20px;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  background-color: #ffffff;
  overflow: hidden;
  user-select: none;
}

.properties-panel {
  width: 280px;
  border-left: 1px solid #e1e8ed;
  padding: 15px;
  overflow-y: auto;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.properties-form {
  margin-top: 15px;
}

.property-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e1e8ed;
}

.property-section:last-child {
  border-bottom: none;
}

.property-section h5 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.property-row {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.property-item {
  flex: 1;
  margin-bottom: 15px;
}

.property-row .property-item {
  margin-bottom: 0;
}

.property-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #666;
}

.property-item input[type="text"],
.property-item input[type="number"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.property-item input[type="checkbox"] {
  margin-right: 8px;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-style: italic;
}

.design-element {
  position: absolute;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: move;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  min-height: 20px;
}

.design-element:hover {
  border-color: #67c23a;
}

.design-element.selected {
  border-color: #41b883;
  box-shadow: 0 0 0 1px #41b883;
}

.design-element.dragging {
  opacity: 0.8;
  z-index: 1000;
}

/* 元素控制器 */
.element-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 调整大小控制点 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  background-color: #41b883;
  border: 1px solid white;
  border-radius: 2px;
  pointer-events: all;
  z-index: 10;
}

/* 角落控制点 */
.resize-nw {
  top: -4px;
  left: -4px;
  width: 8px;
  height: 8px;
  cursor: nw-resize;
}

.resize-ne {
  top: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  cursor: ne-resize;
}

.resize-sw {
  bottom: -4px;
  left: -4px;
  width: 8px;
  height: 8px;
  cursor: sw-resize;
}

.resize-se {
  bottom: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  cursor: se-resize;
}

/* 边缘控制点 */
.resize-n {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  cursor: n-resize;
}

.resize-s {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  cursor: s-resize;
}

.resize-w {
  top: 50%;
  left: -4px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  cursor: w-resize;
}

.resize-e {
  top: 50%;
  right: -4px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  cursor: e-resize;
}

/* 管理按钮样式 */
.manage-button {
  padding: 6px 12px;
  background-color: #67c23a;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.manage-button:hover {
  background-color: #5daf34;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #41b883;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.modal-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.modal-actions button[type="button"] {
  background-color: #f5f5f5;
  color: #333;
}

.modal-actions button[type="submit"] {
  background-color: #41b883;
  color: white;
}

.modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-actions button:hover:not(:disabled) {
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .materials-panel {
    width: 200px;
  }

  .properties-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .editor-main {
    flex-direction: column;
  }

  .materials-panel,
  .properties-panel {
    width: 100%;
    height: 200px;
    border: none;
    border-bottom: 1px solid #e1e8ed;
  }

  .renderer {
    flex: 1;
    min-height: 400px;
  }

  .canvas {
    min-height: 300px;
  }
}
</style>
