"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const form_entity_1 = require("../entities/form.entity");
let FormsService = class FormsService {
    formsRepository;
    constructor(formsRepository) {
        this.formsRepository = formsRepository;
    }
    findAll() {
        return this.formsRepository.find({
            order: { updatedAt: 'DESC' }
        });
    }
    findOne(id) {
        return this.formsRepository.findOneBy({ id });
    }
    async create(createFormDto) {
        const form = this.formsRepository.create({
            ...createFormDto,
            status: createFormDto.status || 'draft'
        });
        return this.formsRepository.save(form);
    }
    async update(id, updateFormDto) {
        await this.formsRepository.update(id, updateFormDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.formsRepository.delete(id);
    }
    async duplicate(id) {
        const originalForm = await this.findOne(id);
        if (!originalForm) {
            return null;
        }
        const duplicatedForm = this.formsRepository.create({
            name: `${originalForm.name} (副本)`,
            description: originalForm.description,
            content: originalForm.content,
            status: 'draft'
        });
        return this.formsRepository.save(duplicatedForm);
    }
};
exports.FormsService = FormsService;
exports.FormsService = FormsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(form_entity_1.Form)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], FormsService);
//# sourceMappingURL=forms.service.js.map