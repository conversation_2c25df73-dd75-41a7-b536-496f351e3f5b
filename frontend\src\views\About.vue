<template>
  <div class="about">
    <h1>关于我们</h1>
    <p>这是一个使用Vue 3和Vue Router构建的示例应用。</p>
    <p>您可以使用这个项目作为您自己应用的起点。</p>
    <div class="info-box">
      <h3>技术栈</h3>
      <ul>
        <li>Vue 3 - 渐进式JavaScript框架</li>
        <li>Vite - 下一代前端构建工具</li>
        <li>Vue Router 4 - Vue.js官方路由</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
</script>

<style scoped>
.about {
  height: 100%;
  width: 100%;
  padding: 40px;
  overflow-y: auto;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

h1 {
  color: white;
  margin-bottom: 30px;
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-align: center;
}

p {
  margin-bottom: 20px;
  line-height: 1.8;
  font-size: 1.1rem;
  opacity: 0.9;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.info-box {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 12px;
  margin-top: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.info-box h3 {
  color: white;
  margin-bottom: 20px;
  font-size: 1.5rem;
  text-align: center;
}

ul {
  padding-left: 0;
  list-style: none;
}

li {
  margin-bottom: 15px;
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border-left: 4px solid rgba(255, 255, 255, 0.3);
}
</style>
