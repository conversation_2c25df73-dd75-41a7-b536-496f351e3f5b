<template>
  <div class="el-text" :style="{ 
    fontSize: `${fontSize}px`, 
    fontWeight: bold ? 'bold' : 'normal',
    color: color,
    textAlign: align
  }">
    {{ content }}
  </div>
</template>

<script setup>
defineProps({
  content: {
    type: String,
    default: '文本内容'
  },
  fontSize: {
    type: Number,
    default: 14
  },
  color: {
    type: String,
    default: '#303133'
  },
  bold: {
    type: Boolean,
    default: false
  },
  align: {
    type: String,
    default: 'left'
  }
});
</script>

<style scoped>
.el-text {
  line-height: 1.5;
  margin: 0;
  padding: 5px 0;
}
</style>
