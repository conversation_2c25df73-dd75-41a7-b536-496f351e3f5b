<script setup>
// 不需要导入任何组件，因为我们使用路由来管理页面
</script>

<template>
  <div class="app">
    <header>
      <nav class="navbar">
        <div class="logo-container">
          <img src="./assets/vue.svg" class="logo" alt="Vue logo" />
          <h1>低代码平台</h1>
        </div>
        <div class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/editor" class="nav-link">编辑器</router-link>
          <router-link to="/forms" class="nav-link">表单管理</router-link>
          <router-link to="/about" class="nav-link">关于我们</router-link>
        </div>
      </nav>
    </header>

    <main>
      <!-- 路由视图 - 这里将显示匹配的路由组件 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <footer>
      <p>
        &copy; {{ new Date().getFullYear() }} 低代码平台 | 使用 Vue 3 + Vite +
        Vue Router 构建
      </p>
    </footer>
  </div>
</template>

<style>
/* 全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #35495e;
  color: white;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 2.5em;
  margin-right: 10px;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.router-link-active {
  background-color: #41b883;
}

/* 主内容区域 */
main {
  flex: 1;
  padding: 20px;
}

/* 页脚样式 */
footer {
  background-color: #f5f5f5;
  text-align: center;
  padding: 1rem;
  margin-top: auto;
  color: #666;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
