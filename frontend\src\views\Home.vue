<template>
  <div class="home">
    <h1>低代码平台</h1>
    <p>欢迎使用我们的低代码开发平台，快速构建您的应用</p>

    <div class="action-buttons">
      <router-link to="/editor" class="action-button primary">
        <span class="icon">🛠️</span>
        进入编辑器
      </router-link>
      <router-link to="/forms" class="action-button secondary">
        <span class="icon">📝</span>
        管理表单
      </router-link>
    </div>

    <div class="features">
      <div class="feature">
        <h3>拖拽设计</h3>
        <p>通过简单的拖拽操作设计界面</p>
      </div>
      <div class="feature">
        <h3>可视化编辑</h3>
        <p>所见即所得的编辑体验</p>
      </div>
      <div class="feature">
        <h3>组件丰富</h3>
        <p>提供多种常用组件</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
</script>

<style lang="less" scoped>
// 变量定义
@primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@primary-color: #41b883;
@secondary-color: #35495e;
@dark-color: #2c3e50;
@success-color: #67c23a;
@success-dark: #5daf34;
@green-color: #4caf50;
@white: #fff;
@white-transparent: rgba(255, 255, 255, 0.1);
@white-border: rgba(255, 255, 255, 0.2);
@white-text: rgba(255, 255, 255, 0.8);
@shadow-light: rgba(0, 0, 0, 0.2);
@shadow-dark: rgba(0, 0, 0, 0.3);

.home {
  height: 100%;
  width: 100%;
  padding: 40px;
  overflow-y: auto;
  background: @primary-gradient;
  color: @white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;

  p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
  }
}

h1 {
  color: @white;
  margin-bottom: 20px;
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 0 2px 4px @shadow-dark;
}

.features {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 60px;
  flex-wrap: wrap;
  max-width: 900px;
}

.feature {
  background-color: @white-transparent;
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 12px;
  width: 280px;
  border: 1px solid @white-border;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px @shadow-light;
  }

  h3 {
    color: @white;
    margin-bottom: 15px;
    font-size: 1.3rem;
  }

  p {
    color: @white-text;
    line-height: 1.6;
  }
}

.action-buttons {
  margin: 40px 0;
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 36px;
  color: @white;
  text-decoration: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 180px;
  justify-content: center;
  box-shadow: 0 8px 25px @shadow-light;
  backdrop-filter: blur(10px);
  border: 2px solid @white-border;

  .icon {
    font-size: 20px;
  }

  &.primary {
    background: linear-gradient(45deg, @primary-color, @secondary-color);

    &:hover {
      background: linear-gradient(45deg, @secondary-color, @dark-color);
      transform: translateY(-3px);
      box-shadow: 0 12px 30px @shadow-dark;
    }
  }

  &.secondary {
    background: linear-gradient(45deg, @success-color, @success-dark);

    &:hover {
      background: linear-gradient(45deg, @success-dark, @green-color);
      transform: translateY(-3px);
      box-shadow: 0 12px 30px @shadow-dark;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feature {
    width: 100%;
  }
}
</style>
