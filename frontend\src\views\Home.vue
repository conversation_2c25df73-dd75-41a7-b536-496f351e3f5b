<template>
  <div class="home">
    <h1>低代码平台</h1>
    <p>欢迎使用我们的低代码开发平台，快速构建您的应用</p>

    <div class="action-buttons">
      <router-link to="/editor" class="action-button primary">
        <span class="icon">🛠️</span>
        进入编辑器
      </router-link>
      <router-link to="/forms" class="action-button secondary">
        <span class="icon">📝</span>
        管理表单
      </router-link>
    </div>

    <div class="features">
      <div class="feature">
        <h3>拖拽设计</h3>
        <p>通过简单的拖拽操作设计界面</p>
      </div>
      <div class="feature">
        <h3>可视化编辑</h3>
        <p>所见即所得的编辑体验</p>
      </div>
      <div class="feature">
        <h3>组件丰富</h3>
        <p>提供多种常用组件</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
</script>

<style scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #41b883;
  margin-bottom: 20px;
}

.features {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  flex-wrap: wrap;
}

.feature {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  width: 30%;
  min-width: 200px;
  margin-bottom: 20px;
}

.feature h3 {
  color: #35495e;
  margin-bottom: 10px;
}

.action-buttons {
  margin: 40px 0;
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s;
  min-width: 160px;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.action-button.primary {
  background-color: #41b883;
}

.action-button.primary:hover {
  background-color: #35495e;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.action-button.secondary {
  background-color: #67c23a;
}

.action-button.secondary:hover {
  background-color: #5daf34;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.action-button .icon {
  font-size: 20px;
}

@media (max-width: 768px) {
  .feature {
    width: 100%;
  }
}
</style>
