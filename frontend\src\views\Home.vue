<template>
  <div class="home">
    <h1>低代码平台</h1>
    <p>欢迎使用我们的低代码开发平台，快速构建您的应用</p>

    <div class="action-buttons">
      <router-link to="/editor" class="action-button primary">
        <span class="icon">🛠️</span>
        进入编辑器
      </router-link>
      <router-link to="/forms" class="action-button secondary">
        <span class="icon">📝</span>
        管理表单
      </router-link>
    </div>

    <div class="features">
      <div class="feature">
        <h3>拖拽设计</h3>
        <p>通过简单的拖拽操作设计界面</p>
      </div>
      <div class="feature">
        <h3>可视化编辑</h3>
        <p>所见即所得的编辑体验</p>
      </div>
      <div class="feature">
        <h3>组件丰富</h3>
        <p>提供多种常用组件</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
</script>

<style scoped>
.home {
  height: 100%;
  width: 100%;
  padding: 40px;
  overflow-y: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

h1 {
  color: white;
  margin-bottom: 20px;
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.home p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.features {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 60px;
  flex-wrap: wrap;
  max-width: 900px;
}

.feature {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 12px;
  width: 280px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.feature h3 {
  color: white;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.feature p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.action-buttons {
  margin: 40px 0;
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 36px;
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 180px;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.action-button.primary {
  background: linear-gradient(45deg, #41b883, #35495e);
}

.action-button.primary:hover {
  background: linear-gradient(45deg, #35495e, #2c3e50);
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.action-button.secondary {
  background: linear-gradient(45deg, #67c23a, #5daf34);
}

.action-button.secondary:hover {
  background: linear-gradient(45deg, #5daf34, #4caf50);
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.action-button .icon {
  font-size: 20px;
}

@media (max-width: 768px) {
  .feature {
    width: 100%;
  }
}
</style>
